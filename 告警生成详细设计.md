# 告警生成详细设计

## 目录

- [告警生成详细设计](#告警生成详细设计)
  - [目录](#目录)
  - [1. 系统概述](#1-系统概述)
  - [2. 指标数据到事件到告警的整体流程](#2-指标数据到事件到告警的整体流程)
    - [2.1 流程概述](#21-流程概述)
    - [2.2 详细流程说明](#22-详细流程说明)
  - [3. 核心组件设计](#3-核心组件设计)
    - [3.1 监控规则评估器](#31-监控规则评估器)
      - [3.1.1 单指标监控器 (SingleMetricMonitorTimer)](#311-单指标监控器-singlemetricmonitortimer)
      - [3.1.2 多指标监控器 (MultipleMetricMonitorTimer)](#312-多指标监控器-multiplemetricmonitortimer)
      - [3.1.3 阈值检测 (ThresholdAlarmCheckMMT)](#313-阈值检测-thresholdalarmcheckmmt)
      - [3.1.4 变更点检测 (ChangePointAlarmCheck)](#314-变更点检测-changepointalarmcheck)
    - [3.2 事件处理器](#32-事件处理器)
      - [3.2.1 事件生成 (MonitorUtils)](#321-事件生成-monitorutils)
      - [3.2.2 事件收敛 (DCEventConvergenceConsumer)](#322-事件收敛-dceventconvergenceconsumer)
      - [3.2.3 收敛策略](#323-收敛策略)
    - [3.3 告警处理器](#33-告警处理器)
      - [3.3.1 告警生成 (DCAlarmConsumer)](#331-告警生成-dcalarmconsumer)
      - [3.3.2 告警生命周期](#332-告警生命周期)
      - [3.3.3 告警清理 (DCAlarmCleanService)](#333-告警清理-dcalarmcleanservice)
    - [3.4 指标反馈系统](#34-指标反馈系统)
      - [3.4.1 告警指标统计](#341-告警指标统计)
      - [3.4.2 自监控](#342-自监控)
  - [4. 数据结构设计](#4-数据结构设计)
    - [4.1 事件数据结构 (DCEventDto)](#41-事件数据结构-dceventdto)
    - [4.2 告警数据结构 (DCAlarmDto)](#42-告警数据结构-dcalarmdto)
    - [4.3 告警聚合数据结构 (DcAlarmAggregate)](#43-告警聚合数据结构-dcalarmaggregate)
  - [5. 配置和扩展](#5-配置和扩展)
    - [5.1 监控规则配置](#51-监控规则配置)
    - [5.2 告警转发配置](#52-告警转发配置)
    - [5.3 自定义功能配置](#53-自定义功能配置)
  - [6. 性能和可靠性设计](#6-性能和可靠性设计)
    - [6.1 性能优化](#61-性能优化)
    - [6.2 可靠性保障](#62-可靠性保障)
    - [6.3 可配置性](#63-可配置性)
  - [7. 总结](#7-总结)

## 1. 系统概述

告警系统是DataBuff平台的核心组件，负责将收集的指标数据转化为有意义的事件和告警，帮助用户及时发现和解决问题。系统采用流处理架构，通过Kafka消息队列实现各组件间的解耦和高可用性。

## 2. 指标数据到事件到告警的整体流程

### 2.1 流程概述

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  指标数据收集     |---->|  监控规则评估     |---->|  事件生成        |
|  (Metric Data)   |     |  (Rule Evaluation)|     |  (Event Generation)|
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
                                                          |
                                                          v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  告警统计和反馈   |<----|  告警生成        |<----|  事件收敛        |
|  (Alarm Metrics) |     |  (Alarm Generation)|   |  (Event Convergence)|
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
        |                        |
        v                        v
+------------------+     +------------------+
|                  |     |                  |
|  自监控指标      |     |  告警通知        |
|  (Self-Monitoring)|    |  (Notification)  |
|                  |     |                  |
+------------------+     +------------------+
```

### 2.2 详细流程说明

1. **指标数据收集**
    - 系统从各种来源收集指标数据，包括主机、服务、中间件等
    - 指标数据通过Kafka进入系统，存储在时序数据库中

2. **监控规则评估**
    - `SingleMetricMonitorTimer` 和 `MultipleMetricMonitorTimer` 负责评估单指标和多指标监控规则
    - 监控规则从 `dc_databuff_monitor` 表中获取配置
    - 支持多种检测方式：阈值检测、变更点检测等

3. **事件生成**
    - 当指标数据触发监控规则时，生成事件(Event)
    - 事件包含触发信息、标签、时间戳等
    - 事件通过 `monitorUtils.eventSinkDBMq()` 方法发送到数据库和消息队列
    - 事件ID通过 `EventIdGenerator` 生成，格式为 "E" + 分类首字母 + 日期 + 序号

4. **事件收敛**
    - `DCEventConvergenceConsumer` 处理事件收敛
    - 支持多种收敛策略：固定时长窗口、无事件窗口、智能窗口等
    - 事件收敛后通过Kafka发送到 `EVENT_CONVERGENCE_TOPIC`

5. **告警生成**
    - `DCAlarmConsumer` 消费告警主题数据
    - 根据事件生成告警，包括首次触发、持续触发和最后一次触发的处理
    - 告警通过 `kafkaUtil.producerSend(ALARM_TOPIC, alarmId, jsonObject)` 发送
    - 告警ID格式为 "A" + 日期 + 序号

6. **告警统计和指标反馈**
    - 告警生成后，系统会统计告警指标
    - 通过 `countCreateAlarmMetric`、`countTriggerAlarmMetric` 和 `countLastAlarmMetric` 方法记录告警相关指标
    - 这些指标通过 `OtelMetricUtil` 上报到监控系统

7. **告警通知**
    - 支持通过Kafka/MQTT将告警转发到外部系统
    - 可配置是否启用首次告警、最后一次告警或所有告警的转发

## 3. 核心组件设计

### 3.1 监控规则评估器

#### 3.1.1 单指标监控器 (SingleMetricMonitorTimer)

[源代码链接](task-executor/src/main/java/com/databuff/tasks/monitor/SingleMetricMonitorTimer.java)

负责评估单一指标的监控规则，主要功能包括：

- 从时序数据库查询指标数据
- 根据配置的检测方式和阈值进行评估
- 支持无数据场景的处理
- 生成事件并发送到消息队列

关键代码片段：

```java
// 单指标监控评估
checkDataRets.putAll(checkOperator.afterCheckNoDataResult(new DatabuffMonitor(m), "1",noDataResult,query));
// 事件记录
JSONObject event = this.getEventLogJson(eventEntity, m, query, tags);
event.

put(TAGS, tags);
event.

put("cycle",period);
event.

put(BY, query.findBy());
        events.

add(event);
//事件告警，故障场景，连续性，以及变量填充后发送库及队列
monitorUtils.

eventSinkDBMq(events, m);
```

#### 3.1.2 多指标监控器 (MultipleMetricMonitorTimer)

[源代码链接](task-executor/src/main/java/com/databuff/tasks/monitor/MultipleMetricMonitorTimer.java)

负责评估多个指标组合的监控规则，主要功能包括：

- 支持多指标组合条件的评估
- 处理复杂的指标关系和依赖
- 生成事件并发送到消息队列

#### 3.1.3 阈值检测 (ThresholdAlarmCheckMMT)

[源代码链接](databuff-dao/src/main/java/com/databuff/metric/impl/alarm/ThresholdAlarmCheckMMT.java)

负责基于阈值的告警检测，支持：

- 关键(critical)和警告(warning)两级阈值
- 多种比较运算符(>、>=、<、<=、==)
- 无数据场景的处理

关键代码片段：

```java
/** 状态分级逻辑：
 * - 当critical阈值存在且满足时，标记为重要告警(3)
 * - 否则当warning阈值存在且满足时，标记为次要告警(2)
 * - 都不满足时保持正常状态(0)
 * */
if(conditionMet){
status =(critical !=null&&

checkCriticalCondition(value, critical, comparison))?CRITICAL_STATUS :WARNING_STATUS;
threshold =(status ==CRITICAL_STATUS)?critical.

toString() :warning.

toString();
}
```

#### 3.1.4 变更点检测 (ChangePointAlarmCheck)

[源代码链接](databuff-dao/src/main/java/com/databuff/metric/impl/alarmV2/ChangePointAlarmCheck.java)

负责基于数据变更点的告警检测，支持：

- 通过API调用外部服务进行变更点检测
- 可配置检测超时和URL
- 支持预览功能

### 3.2 事件处理器

#### 3.2.1 事件生成 (MonitorUtils)

[源代码链接](task-executor/src/main/java/com/databuff/tasks/util/MonitorUtils.java)

负责生成事件并发送到消息队列，主要功能包括：

- 初始化事件记录JSON
- 处理事件消息中的变量替换
- 发送事件到数据库和消息队列

#### 3.2.2 事件收敛 (DCEventConvergenceConsumer)

[源代码链接](task-executor/src/main/java/com/databuff/tasks/kafka/stream/DCEventConvergenceConsumer.java)

负责处理事件收敛，防止告警风暴，主要功能包括：

- 消费 `EVENT_CONVERGENCE_TOPIC` 主题的事件
- 根据收敛策略进行事件收敛
- 将收敛后的事件发送到告警主题

关键代码片段：

```java

@KafkaListener(containerFactory = "kafkaListenerContainerFactory", groupId = KafkaTopicConstant.EVENT_CONVERGENCE_TOPIC, topics = KafkaTopicConstant.EVENT_CONVERGENCE_TOPIC, concurrency = "1")
public void onEventNotify(List<ConsumerRecord<String, String>> records) {
    if (!alarmConfig.getSubscribe().isEnabled()) {
        log.debug("订阅功能未启用，跳过本次{}条消息处理", records.size());
        return;
    }
    log.info("收到{}条事件收敛请求", records.size());
    doProcess(records);
}
```

#### 3.2.3 收敛策略

[源代码链接](databuff-dao/src/main/java/com/databuff/tasks/convergence/ConvergenceType.java)

系统支持多种收敛策略：

- **固定时长窗口**：在固定时间窗口内收敛事件
- **无事件窗口**：在无新事件的情况下结束收敛
- **智能窗口**：基于根因分析的智能收敛
- **默认收敛**：基本收敛策略

### 3.3 告警处理器

#### 3.3.1 告警生成 (DCAlarmConsumer)

[源代码链接](task-executor/src/main/java/com/databuff/tasks/kafka/stream/DCAlarmConsumer.java)

负责生成告警并处理告警生命周期，主要功能包括：

- 消费 `ALARM_TOPIC` 主题的数据
- 处理告警的首次触发、持续触发和最后一次触发
- 统计告警指标
- 触发根因分析

关键代码片段：

```java

@KafkaListener(containerFactory = "kafkaListenerContainerFactory", groupId = "dc_alarm", topics = KafkaTopicConstant.ALARM_TOPIC)
public void onAlarmMetricNotify(List<ConsumerRecord<String, String>> records) {
    doProcess(records, dcAlarmDto -> {
        try {
            countMetrics(dcAlarmDto);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    });
}
```

#### 3.3.2 告警生命周期

[源代码链接](databuff-dao/src/main/java/com/databuff/entity/dto/DCAlarmDto.java)

告警生命周期包括：

- **首次触发(first)**：告警首次生成时
- **持续触发**：告警持续存在时
- **最后一次触发(last)**：告警结束时

关键代码片段：

```java
final boolean first = alarm.getFirst();
final boolean last = alarm.getLast();
if(first){

countTriggerAlarmMetric(alarm);

countHealthCheckMetric(alarm, apiKey);

countCreateAlarmSelfMonitor(alarm);

countCreateAlarmMetric(alarm);
}else if(last){

countLastAlarmMetric(alarm, apiKey);
}else{

countTriggerAlarmMetric(alarm);
}
```

#### 3.3.3 告警清理 (DCAlarmCleanService)

[源代码链接](task-executor/src/main/java/com/databuff/tasks/service/impl/DCAlarmCleanService.java)

负责清理过期告警，主要功能包括：

- 定期检查过期告警
- 标记过期告警为最后一次触发
- 更新告警状态和持续时间
- 发送最后一次告警通知

关键代码片段：

```java
// 2.如果告警信息不为空，标记为最后一个告警
cacheAlarm.setFirst(false);
cacheAlarm.

setLast(true);
// 设置告警结束时间
cacheAlarm.

setEndTriggerTime(System.currentTimeMillis() -(taskRefreshScopeConfig.

getEventDefTimeOffset() -60)*1000);
// 发送告警到Kafka
        kafkaUtil.

producerSend(ALARM_TOPIC, alarmId, (JSONObject) JSONObject.

toJSON(cacheAlarm));
```

### 3.4 指标反馈系统

#### 3.4.1 告警指标统计

[源代码链接](task-executor/src/main/java/com/databuff/tasks/kafka/stream/DCAlarmConsumer.java#L200-L250)

负责统计告警相关指标，主要功能包括：

- 记录告警创建次数
- 记录告警触发次数
- 记录告警结束次数
- 记录健康检查指标

关键代码片段：

```java
public void countCreateAlarmMetric(DCAlarmDto alarmDto) {
    Map<String, Object> tag = extractTags(alarmDto);
    final Map<String, Object> value = new HashMap<>();
    value.put("created.cnt", 1L);

    JSONObject alarmMetric = MetricsUtil.generateMetric(DF_API_KEY_VALUE, alarmDto.getTimestamp(), "databuff", METRIC_DATABUFF_ALARM, tag, value);
    kafkaUtil.producerSend(METRIC_TOPIC, alarmDto.getId(), alarmMetric);
}
```

#### 3.4.2 自监控

[源代码链接](task-executor/src/main/java/com/databuff/tasks/kafka/stream/DCAlarmConsumer.java#L180-L190)

负责系统自身的监控，主要功能包括：

- 使用OpenTelemetry上报系统自身的运行状态指标
- 监控告警处理性能和延迟
- 监控事件收敛效果

## 4. 数据结构设计

### 4.1 事件数据结构 (DCEventDto)

[源代码链接](databuff-dao/src/main/java/com/databuff/entity/dto/DCEventDto.java)

事件数据包含以下主要字段：

- **id**：事件ID
- **monitorId**：监控规则ID
- **value**：触发值
- **level**：事件等级
- **triggerTime**：触发时间
- **source**：事件来源
- **classification**：分类
- **type**：检测方法
- **message**：事件消息
- **tags**：标签信息
- **trigger**：触发对象

### 4.2 告警数据结构 (DCAlarmDto)

[源代码链接](databuff-dao/src/main/java/com/databuff/entity/dto/DCAlarmDto.java)

告警数据包含以下主要字段：

- **id**：告警ID
- **gid**：域ID
- **apiKey**：API密钥
- **value**：告警实际值
- **duration**：持续时间
- **timestamp**：时间戳
- **startTriggerTime**：开始触发时间
- **endTriggerTime**：结束触发时间
- **alarmMsg**：告警消息
- **description**：描述
- **type**：类型
- **alarmStatus**：告警状态
- **eventId**：事件ID列表
- **eventCnt**：总收敛的事件ID列表数量
- **first**：是否首次告警
- **last**：是否最后一次告警
- **level**：告警等级
- **policy**：收敛策略

### 4.3 告警聚合数据结构 (DcAlarmAggregate)

[源代码链接](databuff-dao/src/main/java/com/databuff/entity/dto/DcAlarmAggregate.java)

告警聚合数据包含以下主要字段：

- **time**：告警时间戳
- **apiKey**：API密钥
- **id**：告警ID
- **gid**：域ID
- **eventCnt**：当前事件数

## 5. 配置和扩展

### 5.1 监控规则配置

监控规则通过 `dc_databuff_monitor` 表配置，包括：

- 监控名称和描述
- 指标查询条件
- 阈值设置
- 检测方式
- 告警消息模板

### 5.2 告警转发配置

[源代码链接](task-executor/src/main/java/com/databuff/tasks/forward/AlarmEventForwardTask.java)

告警转发通过配置文件配置，支持：

- Kafka转发
- MQTT转发
- 自定义HTTP转发

配置示例：

```yaml
databuff:
  alarm:
    forward:
      sdcsh:
        # 是否启用告警转发
        enabled: false
        # 是否启用首次告警处理
        firstEnabled: false
        # 是否启用最后一次告警处理
        lastEnabled: false
        # 是否启用所有告警处理
        allEnabled: true
        # 事件来源标识
        sourceID: databuff
        url: http://webapp:18080/api/alarm/forward/test
        headers:
          Content-Type: application/json;charset=UTF-8
          apiKey: NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4
```

### 5.3 自定义功能配置

[源代码链接](task-executor/src/main/java/com/databuff/tasks/config/CustomFeatureConfig.java)

系统支持通过统一配置开关控制定制功能：

```yaml
databuff:
  custom-features:
    enabled: false # 总开关，默认关闭所有定制功能
    components:
      cmdb-sync: false # CMDB同步开关
      alarm-forward: false # 告警转发开关
```

## 6. 性能和可靠性设计

### 6.1 性能优化

[源代码链接](task-executor/src/main/java/com/databuff/tasks/monitor/MonitorTaskPool.java)

- **批处理**：使用批处理模式处理Kafka消息，提高吞吐量
- **并发处理**：使用多线程和分片处理大量数据
- **缓存优化**：使用Redis缓存频繁访问的数据
- **延迟处理**：支持配置延迟处理，避免过早处理不完整数据

### 6.2 可靠性保障

[源代码链接](task-executor/src/main/java/com/databuff/tasks/convergence/EventCache.java)

- **消息队列**：使用Kafka确保消息可靠传递
- **分布式锁**：使用分布式锁确保并发安全
- **异常处理**：完善的异常捕获和处理机制
- **超时控制**：API调用和处理过程中的超时控制
- **自动清理**：定期清理过期数据，避免资源耗尽

### 6.3 可配置性

[源代码链接](task-executor/src/main/java/com/databuff/tasks/config/TaskRefreshScopeConfig.java)

- **时间偏移量**：可配置默认时间偏移量，适应不同的数据延迟场景
- **收敛策略**：可配置不同的收敛策略和参数
- **告警转发**：可配置告警转发的目标和方式
- **功能开关**：可通过配置文件控制各功能模块的启用/禁用

## 7. 总结

DataBuff告警系统采用流处理架构，通过Kafka消息队列实现各组件间的解耦和高可用性。系统从指标数据收集开始，经过监控规则评估、事件生成、事件收敛、告警生成等环节，最终生成有价值的告警信息，并支持告警转发和指标反馈。

系统设计考虑了性能、可靠性和可配置性，支持多种监控规则和收敛策略，能够适应不同的业务场景和需求。通过统一的配置管理，可以灵活控制各功能模块的启用/禁用，方便系统的维护和扩展。
